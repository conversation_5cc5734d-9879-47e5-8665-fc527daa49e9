@use "lib/viewport";

// these are add-on styles controlled by settings
@import "special-styles";

.welcome-banner:not(.below-site-header-outlet, .above-main-container-outlet),
.header-search--enabled .floating-search-input-wrapper,
.search-header--visible .floating-search-input-wrapper {
  display: none;
}

.search-banner {
  background-image: var(--custom-bg);

  @if $tile-background_image == "true" {
    background-size: auto;
  } @else {
    background-size: cover;
    background-repeat: no-repeat;
  }
}

.welcome-banner {
  .btn.search-icon:not(.has-search-button-text) {
    z-index: 2;
    background: transparent;
    line-height: 1;
    color: var(--primary-medium);
    height: 100%;
    position: absolute;
    left: 0;

    .rtl & {
      right: 0;
      left: unset;
    }

    .discourse-no-touch & {
      &:hover {
        background: transparent;
        color: var(--primary);

        .d-icon {
          color: currentcolor;
        }
      }
    }

    + .search-menu-container .search-input {
      border: 1px solid #3164ac !important;
      padding-left: 1.75em;
      border-radius: 0.5em;
      overflow: hidden;

      .rtl & {
        padding-left: unset;
        padding-right: 1.75em;
      }
    }

    + .search-menu-container .search-input .search-context {
      margin-left: 4px;
    }
  }

  h1 {
    text-align: center;
    margin: 0 auto;
    font-size: var(--font-up-6);
    line-height: 1.3;
    transform: scaleX(1.16);
    transform-origin: center;
    font-weight: 600;
    
    // Desktop/tablet styles
    @include viewport.from(sm) {
      max-width: none; // Remove max-width constraint for larger screens
      white-space: nowrap; // Force single line on larger screens
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    // Mobile styles - keep original behavior
    @include viewport.until(sm) {
      transform: scaleX(1.05);
      font-size: var(--font-up-4);
      max-width: 600px; // Keep original max-width for mobile
      white-space: normal; // Allow text to wrap on mobile 
    }
  }

  .btn.search-icon.has-search-button-text {
    margin-left: 0.5em;
    column-gap: 0.5em;
    background-color: var(--tertiary);
    color: var(--secondary);
    position: unset;
    height: unset;

    &:hover {
      background-color: var(--tertiary-hover);
      color: var(--secondary);
    }

    .d-icon {
      color: var(--secondary);
    }
  }
}

.custom-search-banner-wrap {
  padding: 1em var(--d-wrap-padding-h) 1.25em;

  // Mobile styles only
  @include viewport.until(sm) {
    padding: 3em var(--d-wrap-padding-h) 3em;  // Increased top padding for mobile
  }

  h1 {
    margin-bottom: 0.3em;
    padding-top: 0.5em;
  }

  p {
    margin-top: 0;
  }

  @include viewport.from(sm) {
    padding: 2.5em var(--d-wrap-padding-h) 3em;
  }
}

// fixes core style clash that hides the banner on narrow screens
.custom-search-banner .welcome-banner__wrap .search-menu {
  display: flex;
}
