@if $special-style == "big search, no text" {
  .welcome-banner {
    margin-bottom: 0;
  }

  .above-main-container-outlet .custom-search-banner-wrap {
    padding: 0 0 1em 0;
  }

  .custom-search-banner-wrap {
    padding: 1em;

    @if $background-image-light != "" {
      background-image: var(--custom-bg);
      padding: 4em 4em 4.25em;

      .search-input {
        box-shadow: shadow("dropdown");
      }
    }

    .results {
      max-width: unset;
      width: 100%;
      font-size: var(--font-down-3);
      top: 3.45em;
      padding: 0;

      .search-result-topic .first-line {
        font-size: var(--font-up-1);
      }
    }

    .search-context {
      max-width: unset;
      width: auto;
    }

    > h1,
    > p {
      display: none;
    }

    > div {
      font-size: var(--font-up-4);
      max-width: unset;
    }

    .blurb {
      font-size: var(--font-down-2);
      max-width: 600px;
      color: var(--primary-high);
    }

    .second-line,
    .discourse-tags {
      font-size: var(--font-down-1);
    }

    .search-menu-recent .heading {
      margin: 0 0 0.75em 0;
      padding: 0.33em 1em;
      font-size: var(--font-up-1);
    }

    .search-menu-recent {
      font-size: var(--font-down-1);
      margin-top: 0;

      li:last-child {
        .search-link {
          margin-bottom: 0;
        }
      }
    }

    .search-random-quick-tip {
      padding: 1em;
      font-size: var(--font-down-1);
    }

    .no-results {
      padding: 1em !important;
    }

    .results > * {
      padding: 0 !important;
      margin: 0 !important;

      li:first-child {
        .search-link {
          margin-top: 0;
        }
      }

      li:last-child {
        .search-link {
          margin-bottom: 0;
        }
      }

      .search-link {
        margin: -1em 0 0.33em 0;
        padding: 1em;
      }

      .show-more {
        margin-top: 0.33em;

        .search-link {
          margin-bottom: 0;
        }
      }
    }

    .results .search-menu-assistant-item {
      font-size: var(--font-0);
    }

    .search-input .searching {
      top: 0.45em;
    }
  }
}
